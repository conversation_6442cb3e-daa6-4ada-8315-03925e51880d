# 🚀 造价管理系统启动说明

## 📋 系统架构

本系统采用微前端架构，包含以下组件：

- **主应用**: Tauri 桌面应用 (端口: 主窗口)
- **概算模块**: Vue3 + Ant Design Vue (端口: 5174)
- **其他模块**: 预算、审核等 (开发中)

## 🎯 快速启动

### 方法一：使用启动脚本 (推荐)

#### Windows 批处理文件
```bash
# 双击运行
start-all.bat
```

#### PowerShell 脚本
```powershell
# 右键 -> 使用 PowerShell 运行
.\start-all.ps1
```

### 方法二：手动启动

#### 1. 启动概算模块
```bash
cd packages/rough-estimate
npm run dev
```
访问: http://localhost:5174

#### 2. 启动主应用
```bash
# 运行已构建的可执行文件
E:\ysf2.0\moduforge-rs\target\release\demo-app.exe

# 或者开发模式
npm run tauri:dev
```

## 🔧 使用说明

### 主应用功能
1. **启动屏幕**: 应用启动时显示 3 秒加载动画
2. **工作台**: 显示所有业务模块卡片
3. **模块导航**: 点击概算卡片打开概算模块窗口

### 概算模块功能
1. **数据管理**: 概算项目的增删改查
2. **搜索筛选**: 按名称、状态、日期筛选
3. **数据导入导出**: 支持数据的导入和导出

## 🐛 常见问题

### 问题1: 概算模块显示"无法访问此页面"
**原因**: 概算模块服务器未启动
**解决**: 确保概算模块在 5174 端口运行
```bash
cd packages/rough-estimate
npm run dev
```

### 问题2: 主应用无法启动
**原因**: 可执行文件不存在或路径错误
**解决**: 重新构建应用
```bash
npm run tauri:build
```

### 问题3: 启动屏幕不显示
**原因**: splashscreen.html 文件缺失
**解决**: 重新构建前端
```bash
npm run build
```

## 📁 项目结构

```
demo/
├── src/                    # 主应用前端源码
├── src-tauri/             # Tauri 后端源码
├── packages/              # 微前端模块
│   ├── rough-estimate/    # 概算模块
│   ├── budget/           # 预算模块 (开发中)
│   └── shared-components/ # 共享组件
├── dist/                  # 构建输出
├── start-all.bat         # Windows 启动脚本
├── start-all.ps1         # PowerShell 启动脚本
└── 启动说明.md           # 本文件
```

## 🎨 界面特性

### 主应用
- 紫色渐变标题栏
- 6个业务模块卡片
- 无边框窗口设计
- 自定义窗口控制

### 概算模块
- 蓝色渐变标题栏
- 响应式数据表格
- 实时搜索和筛选
- 现代化 UI 设计

## 🔄 开发模式

### 热重载开发
```bash
# 终端1: 启动概算模块
cd packages/rough-estimate
npm run dev

# 终端2: 启动主应用开发模式
npm run tauri:dev
```

### 构建生产版本
```bash
# 构建前端
npm run build

# 构建 Tauri 应用
npm run tauri:build
```

## 📞 技术支持

如遇到问题，请检查：
1. Node.js 版本 >= 18.19.0
2. 所有依赖已正确安装
3. 端口 5174 未被占用
4. 防火墙设置允许本地连接
