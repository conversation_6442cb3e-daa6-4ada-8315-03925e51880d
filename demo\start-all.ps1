Write-Host "启动造价管理系统..." -ForegroundColor Green

Write-Host ""
Write-Host "1. 启动概算模块服务器..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'packages\rough-estimate'; npx vite --port 5174"

Write-Host ""
Write-Host "2. 等待服务器启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

Write-Host ""
Write-Host "3. 启动主应用..." -ForegroundColor Yellow
Start-Process "E:\ysf2.0\moduforge-rs\target\release\demo-app.exe"

Write-Host ""
Write-Host "所有服务已启动！" -ForegroundColor Green
Write-Host "- 概算模块: http://localhost:5174" -ForegroundColor Cyan
Write-Host "- 主应用: 桌面应用" -ForegroundColor Cyan
Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
